package com.example.demo.dao;

import com.example.demo.model.HealthCheck;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * 体检指标数据访问层
 * 负责与数据库的交互操作
 */
@Repository
public class HealthCheckDAO {

    private static final Logger logger = LoggerFactory.getLogger(HealthCheckDAO.class);
    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public HealthCheckDAO(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 根据姓名查询体检指标（返回最新的一条记录）
     * @param personName 人员姓名
     * @return 体检数据，如果未找到返回null
     */
    public HealthCheck findByPersonName(String personName) {
        String sql = "SELECT * FROM health_check WHERE person_name = ? ORDER BY check_date DESC LIMIT 1";

        try {
            logger.debug("执行查询SQL: {} with parameter: {}", sql, personName);
            List<HealthCheck> results = jdbcTemplate.query(sql, new HealthCheckRowMapper(), personName);

            if (results.isEmpty()) {
                logger.debug("未找到姓名为 {} 的体检记录", personName);
                return null;
            }

            HealthCheck result = results.get(0);
            logger.debug("查询到体检记录: ID={}, 姓名={}, 检查日期={}",
                result.getId(), result.getPersonName(), result.getCheckDate());
            return result;

        } catch (EmptyResultDataAccessException e) {
            logger.debug("未找到姓名为 {} 的体检记录", personName);
            return null;
        } catch (Exception e) {
            logger.error("查询体检数据时发生错误: personName={}", personName, e);
            throw e;
        }
    }

    /**
     * 保存体检指标
     * @param healthCheck 体检数据
     * @return 影响的行数
     */
    public int save(HealthCheck healthCheck) {
        String sql = "INSERT INTO health_check (person_name, gender, age, check_date, wbc_count, " +
                "neutrophil_pct, lymphocyte_pct, monocyte_pct, neutrophil_count) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try {
            logger.debug("执行插入SQL: {}", sql);
            logger.debug("插入参数: 姓名={}, 性别={}, 年龄={}",
                healthCheck.getPersonName(), healthCheck.getGender(), healthCheck.getAge());

            int rowsAffected = jdbcTemplate.update(sql,
                    healthCheck.getPersonName(),
                    healthCheck.getGender(),
                    healthCheck.getAge(),
                    healthCheck.getCheckDate(),
                    healthCheck.getWbcCount(),
                    healthCheck.getNeutrophilPct(),
                    healthCheck.getLymphocytePct(),
                    healthCheck.getMonocytePct(),
                    healthCheck.getNeutrophilCount());

            logger.debug("插入操作影响了 {} 行", rowsAffected);
            return rowsAffected;

        } catch (Exception e) {
            logger.error("保存体检数据时发生错误: {}", healthCheck.getPersonName(), e);
            throw e;
        }
    }

    /**
     * 行映射器 - 将数据库记录映射为HealthCheck对象
     */
    private static class HealthCheckRowMapper implements RowMapper<HealthCheck> {
        @Override
        public HealthCheck mapRow(ResultSet rs, int rowNum) throws SQLException {
            HealthCheck healthCheck = new HealthCheck();

            try {
                healthCheck.setId(rs.getLong("id"));
                healthCheck.setPersonName(rs.getString("person_name"));
                healthCheck.setGender(rs.getString("gender"));

                // 处理可能为null的整数字段
                int age = rs.getInt("age");
                healthCheck.setAge(rs.wasNull() ? null : age);

                // 处理日期字段
                java.sql.Date checkDate = rs.getDate("check_date");
                healthCheck.setCheckDate(checkDate != null ? checkDate.toLocalDate() : null);

                // 处理可能为null的浮点数字段
                double wbcCount = rs.getDouble("wbc_count");
                healthCheck.setWbcCount(rs.wasNull() ? null : wbcCount);

                double neutrophilPct = rs.getDouble("neutrophil_pct");
                healthCheck.setNeutrophilPct(rs.wasNull() ? null : neutrophilPct);

                double lymphocytePct = rs.getDouble("lymphocyte_pct");
                healthCheck.setLymphocytePct(rs.wasNull() ? null : lymphocytePct);

                double monocytePct = rs.getDouble("monocyte_pct");
                healthCheck.setMonocytePct(rs.wasNull() ? null : monocytePct);

                double neutrophilCount = rs.getDouble("neutrophil_count");
                healthCheck.setNeutrophilCount(rs.wasNull() ? null : neutrophilCount);

            } catch (SQLException e) {
                logger.error("映射数据库记录时发生错误: rowNum={}", rowNum, e);
                throw e;
            }

            return healthCheck;
        }
    }
}