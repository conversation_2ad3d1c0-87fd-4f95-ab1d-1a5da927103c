package com.example.demo.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.ResponseEntity;
import java.util.Map;

@RestController
public class RootController {

    @GetMapping("/")
    public ResponseEntity<Map<String, Object>> root() {
        return ResponseEntity.ok(Map.of(
                "service", "health-check-mcp",
                "status", "running",
                "endpoints", Map.of(
                        "mcp_rpc", "/mcp/rpc",
                        "mcp_sse", "/mcp/sse",
                        "health", "/mcp/health"
                )
        ));
    }
}