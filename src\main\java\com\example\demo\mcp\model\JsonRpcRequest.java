package com.example.demo.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;

public class JsonRpcRequest {
    @JsonProperty("jsonrpc")
    private String jsonrpc = "2.0";
    
    private String method;
    private Map<String, Object> params;
    private Object id;

    // Getters and setters
    public String getJsonrpc() { return jsonrpc; }
    public void setJsonrpc(String jsonrpc) { this.jsonrpc = jsonrpc; }
    
    public String getMethod() { return method; }
    public void setMethod(String method) { this.method = method; }
    
    public Map<String, Object> getParams() { return params; }
    public void setParams(Map<String, Object> params) { this.params = params; }
    
    public Object getId() { return id; }
    public void setId(Object id) { this.id = id; }
}
