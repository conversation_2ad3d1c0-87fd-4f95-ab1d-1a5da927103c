package com.example.demo.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 统一的CORS跨域配置
 * 支持MCP协议和Dify平台集成
 */
@Configuration
public class CorsConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // MCP协议端点的CORS配置
        registry.addMapping("/mcp/**")
                .allowedOriginPatterns("*")  // 允许所有域名，适配Dify等平台
                .allowedMethods("GET", "POST", "OPTIONS")  // MCP协议需要的HTTP方法
                .allowedHeaders("*")  // 允许所有请求头
                .allowCredentials(false)  // 不允许携带凭证
                .maxAge(3600);  // 预检请求缓存时间

        // 根路径的CORS配置
        registry.addMapping("/")
                .allowedOriginPatterns("*")
                .allowedMethods("GET")
                .allowedHeaders("*")
                .allowCredentials(false);
    }
}